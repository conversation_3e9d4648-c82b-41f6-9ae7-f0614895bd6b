<?php
// TCPDF FONT FILE DESCRIPTION
$type='TrueTypeUnicode';
$name='THSarabunNew-BoldItalic';
$up=-35;
$ut=30;
$dw=698;
$diff='';
$originalsize=118364;
$enc='';
$file='thsarabunnewbi.z';
$ctg='thsarabunnewbi.ctg.z';
$desc=array('Flags'=>96,'FontBBox'=>'[-409 -481 949 844]','ItalicAngle'=>-12,'Ascent'=>844,'Descent'=>-457,'Leading'=>30,'CapHeight'=>476,'XHeight'=>340,'StemV'=>123,'StemH'=>53,'AvgWidth'=>400,'MaxWidth'=>972,'MissingWidth'=>698);
$cbbox=array(0=>array(53,0,645,809),33=>array(-3,-9,177,476),34=>array(88,333,308,526),35=>array(19,0,455,475),36=>array(10,-55,362,533),37=>array(35,-9,665,485),38=>array(6,-14,469,492),39=>array(88,333,187,526),40=>array(14,-55,301,530),41=>array(-43,-55,240,530),42=>array(68,247,343,496),43=>array(18,0,389,363),44=>array(-35,-107,105,92),45=>array(-7,157,213,211),46=>array(-1,-9,102,87),47=>array(-79,-28,361,492),48=>array(12,-9,376,479),49=>array(52,0,291,478),50=>array(-20,0,388,478),51=>array(-10,-8,350,478),52=>array(6,0,352,489),53=>array(-10,-8,390,476),54=>array(11,-8,369,486),55=>array(73,0,411,476),56=>array(1,-8,374,479),57=>array(19,-16,377,478),58=>array(-1,-9,154,333),59=>array(-35,-107,154,333),60=>array(28,-10,419,390),61=>array(22,97,385,275),62=>array(-10,-10,381,390),63=>array(47,-9,310,483),64=>array(-6,-95,554,396),65=>array(-45,0,408,483),66=>array(7,-6,405,482),67=>array(9,-7,462,483),68=>array(7,-4,463,481),69=>array(6,0,390,476),70=>array(6,0,400,476),71=>array(9,-7,462,483),72=>array(6,0,479,476),73=>array(6,0,170,476),74=>array(-47,-9,289,476),75=>array(6,-7,456,484),76=>array(6,0,313,476),77=>array(-15,0,568,476),78=>array(6,0,479,476),79=>array(10,-10,520,485),80=>array(6,0,406,481),81=>array(10,-71,520,485),82=>array(6,0,406,481),83=>array(-17,-9,373,485),84=>array(49,0,474,476),85=>array(4,-9,489,476),86=>array(56,0,480,476),87=>array(53,0,673,476),88=>array(-45,0,479,476),89=>array(51,0,459,476),90=>array(-17,0,480,476),91=>array(-14,-77,272,528),92=>array(36,-27,252,483),93=>array(-30,-77,256,528),94=>array(20,152,382,480),95=>array(-81,-175,306,-122),96=>array(90,375,201,490),97=>array(14,-9,397,353),98=>array(36,-8,406,488),99=>array(1,-9,346,353),100=>array(2,-9,444,504),101=>array(12,-9,362,353),102=>array(-139,-150,338,511),103=>array(-20,-150,331,347),104=>array(6,-15,374,504),105=>array(6,0,181,488),106=>array(-114,-141,190,488),107=>array(6,-10,361,504),108=>array(6,-8,176,504),109=>array(6,-15,597,350),110=>array(6,-15,385,350),111=>array(2,-8,394,353),112=>array(-24,-143,414,353),113=>array(14,-143,397,353),114=>array(6,0,260,350),115=>array(-12,-9,287,349),116=>array(26,-9,277,460),117=>array(31,-9,396,350),118=>array(24,0,390,340),119=>array(24,0,512,340),120=>array(-55,0,379,340),121=>array(-49,-151,392,340),122=>array(-24,0,360,340),123=>array(15,-88,282,498),124=>array(-19,-166,184,526),125=>array(-25,-88,241,498),126=>array(8,144,419,259),161=>array(-25,-141,155,344),162=>array(48,-56,460,514),163=>array(45,0,428,468),164=>array(23,35,464,417),165=>array(71,18,477,461),166=>array(-13,-119,161,476),167=>array(24,-38,355,480),168=>array(95,381,338,469),169=>array(41,-3,527,483),170=>array(52,241,294,478),171=>array(24,47,347,315),172=>array(34,88,403,279),173=>array(-7,157,213,211),174=>array(41,-3,527,483),175=>array(68,528,455,581),176=>array(81,308,275,486),177=>array(-8,0,413,423),178=>array(23,190,267,482),179=>array(37,187,266,483),180=>array(86,366,264,481),181=>array(-26,-141,393,342),182=>array(58,-63,379,475),183=>array(28,134,132,230),184=>array(-20,-159,137,11),185=>array(73,195,222,484),186=>array(39,232,317,487),187=>array(-4,47,320,315),188=>array(39,-9,501,474),189=>array(39,-9,531,474),190=>array(30,-9,501,474),191=>array(-39,-151,223,341),192=>array(-45,0,408,636),193=>array(-45,0,408,636),194=>array(-45,0,408,636),195=>array(-45,0,408,592),196=>array(-45,0,422,605),197=>array(-45,0,408,680),198=>array(-54,0,601,483),199=>array(9,-159,462,483),200=>array(6,0,390,636),201=>array(6,0,390,636),202=>array(6,0,390,636),203=>array(6,0,390,605),204=>array(6,0,213,636),205=>array(6,0,234,636),206=>array(6,0,243,636),207=>array(6,0,283,605),208=>array(7,-4,463,481),209=>array(6,0,479,592),210=>array(10,-10,520,636),211=>array(10,-10,520,636),212=>array(10,-10,520,636),213=>array(10,-10,520,592),214=>array(10,-10,520,605),215=>array(-9,-5,416,372),216=>array(10,-28,520,492),217=>array(4,-9,489,636),218=>array(4,-9,489,636),219=>array(4,-9,489,636),220=>array(4,-9,489,605),221=>array(51,0,459,636),222=>array(9,0,382,476),223=>array(-25,-143,390,492),224=>array(14,-9,397,500),225=>array(14,-9,397,500),226=>array(14,-9,397,500),227=>array(14,-9,397,456),228=>array(14,-9,397,469),229=>array(14,-9,397,544),230=>array(-3,-9,621,354),231=>array(1,-159,346,353),232=>array(12,-9,362,500),233=>array(12,-9,362,500),234=>array(12,-9,362,500),235=>array(12,-9,363,469),236=>array(6,0,185,500),237=>array(6,0,206,500),238=>array(6,0,219,500),239=>array(6,0,254,469),240=>array(7,-16,359,478),241=>array(6,-15,385,456),242=>array(2,-8,394,500),243=>array(2,-8,394,500),244=>array(2,-8,394,500),245=>array(2,-8,394,456),246=>array(2,-8,394,469),247=>array(15,8,391,361),248=>array(2,-28,394,378),249=>array(31,-9,396,500),250=>array(31,-9,396,500),251=>array(31,-9,396,500),252=>array(31,-9,396,469),253=>array(-49,-151,392,500),254=>array(-17,-134,412,476),255=>array(-49,-151,392,469),258=>array(-45,0,432,627),259=>array(14,-9,413,491),262=>array(9,-7,462,636),263=>array(1,-9,346,500),268=>array(9,-7,462,636),269=>array(1,-9,346,500),272=>array(7,-4,463,481),273=>array(2,-9,491,504),286=>array(9,-7,462,627),287=>array(-20,-150,349,491),296=>array(6,0,263,592),297=>array(6,0,233,456),304=>array(6,0,205,589),305=>array(6,0,141,340),321=>array(6,0,353,476),322=>array(7,-8,234,504),338=>array(10,-10,755,486),339=>array(2,-9,691,353),350=>array(-17,-159,373,485),351=>array(-12,-159,287,349),352=>array(-17,-9,373,636),353=>array(-12,-9,310,500),360=>array(4,-9,489,592),361=>array(31,-9,396,456),376=>array(51,0,459,605),381=>array(-17,0,480,636),382=>array(-24,0,360,500),402=>array(-80,-69,465,484),416=>array(10,-10,535,527),417=>array(2,-8,436,379),431=>array(4,-9,603,527),432=>array(31,-9,504,371),710=>array(51,385,241,500),711=>array(121,385,311,500),728=>array(111,379,349,491),729=>array(120,501,215,589),730=>array(100,384,274,544),731=>array(-13,-153,141,19),732=>array(63,367,278,443),733=>array(77,320,366,491),768=>array(-126,385,-15,500),769=>array(-169,385,-16,500),771=>array(-192,380,22,456),777=>array(-124,373,22,539),803=>array(-215,-144,-114,-48),3585=>array(6,-27,366,407),3586=>array(32,-8,436,433),3587=>array(24,-8,452,433),3588=>array(20,-27,379,407),3589=>array(20,-27,388,410),3590=>array(27,-8,474,433),3591=>array(16,0,293,407),3592=>array(39,-8,369,407),3593=>array(17,-8,384,407),3594=>array(37,-8,461,447),3595=>array(29,-8,477,447),3596=>array(0,-8,586,433),3597=>array(14,-207,586,433),3598=>array(-28,-234,399,407),3599=>array(-64,-235,399,407),3600=>array(-97,-230,403,432),3601=>array(24,-27,483,408),3602=>array(26,-8,572,433),3603=>array(14,-8,597,433),3604=>array(25,-27,380,407),3605=>array(22,-27,393,408),3606=>array(14,-27,366,407),3607=>array(29,-27,429,408),3608=>array(5,-8,383,433),3609=>array(29,-8,454,433),3610=>array(29,-8,466,433),3611=>array(29,-8,497,580),3612=>array(-4,0,435,433),3613=>array(-4,0,466,580),3614=>array(29,0,503,433),3615=>array(29,0,534,580),3616=>array(-28,-27,399,407),3617=>array(-9,-8,438,433),3618=>array(-5,-8,407,433),3619=>array(38,-8,353,433),3620=>array(14,-181,366,407),3621=>array(20,-20,367,407),3622=>array(-28,-181,399,407),3623=>array(35,-7,358,407),3624=>array(20,-27,427,447),3625=>array(29,-8,482,433),3626=>array(20,-20,427,447),3627=>array(29,-27,435,407),3628=>array(29,0,525,447),3629=>array(20,-8,373,407),3630=>array(11,-8,416,447),3631=>array(41,-27,368,407),3632=>array(-2,11,342,397),3633=>array(-186,477,142,643),3634=>array(37,-27,322,407),3635=>array(-114,-27,322,634),3636=>array(-312,450,9,618),3637=>array(-312,450,42,648),3638=>array(-312,450,50,658),3639=>array(-312,450,42,648),3640=>array(-245,-271,-110,-39),3641=>array(-365,-247,-85,-35),3642=>array(-170,-111,-94,-41),3647=>array(26,-56,367,522),3648=>array(12,-8,182,433),3649=>array(12,-8,369,433),3650=>array(38,-8,349,662),3651=>array(32,-8,288,659),3652=>array(52,-8,242,666),3653=>array(-96,-181,188,407),3654=>array(77,-172,408,413),3655=>array(-263,464,44,697),3656=>array(-11,677,73,791),3657=>array(-116,667,155,844),3658=>array(-124,668,192,836),3659=>array(-46,677,116,804),3660=>array(-79,639,123,835),3661=>array(-114,465,69,634),3662=>array(-159,455,57,672),3663=>array(31,-6,399,332),3664=>array(23,-9,405,301),3665=>array(19,-20,405,300),3666=>array(-5,0,429,418),3667=>array(-1,-8,422,302),3668=>array(-8,-9,527,418),3669=>array(-8,-9,527,418),3670=>array(31,-9,431,418),3671=>array(-12,-4,505,418),3672=>array(-11,-16,472,403),3673=>array(-2,-10,479,415),3674=>array(41,-27,519,407),3675=>array(56,24,663,340),7840=>array(-45,-144,408,483),7841=>array(14,-144,397,353),7842=>array(-45,0,408,675),7843=>array(14,-9,397,539),7844=>array(-45,0,531,720),7845=>array(14,-9,505,592),7846=>array(-45,0,485,720),7847=>array(14,-9,437,592),7848=>array(-45,0,533,763),7849=>array(14,-9,494,633),7850=>array(-45,0,437,748),7851=>array(14,-9,405,600),7852=>array(-45,-144,408,636),7853=>array(14,-144,397,500),7854=>array(-45,0,444,762),7855=>array(14,-9,425,626),7856=>array(-45,0,432,751),7857=>array(14,-9,413,617),7858=>array(-45,0,432,797),7859=>array(14,-9,413,655),7860=>array(-45,0,438,748),7861=>array(14,-9,416,596),7862=>array(-45,-144,431,627),7863=>array(14,-144,407,491),7864=>array(6,-144,390,476),7865=>array(12,-144,362,353),7866=>array(6,0,390,675),7867=>array(12,-9,362,539),7868=>array(6,0,390,592),7869=>array(12,-9,362,456),7870=>array(6,0,487,735),7871=>array(12,-9,475,602),7872=>array(6,0,423,751),7873=>array(12,-9,421,591),7874=>array(6,0,477,762),7875=>array(12,-9,479,621),7876=>array(6,0,392,747),7877=>array(12,-9,375,593),7878=>array(6,-144,390,636),7879=>array(12,-144,362,500),7880=>array(6,0,250,675),7881=>array(6,0,223,539),7882=>array(-33,-144,170,476),7883=>array(-33,-144,181,488),7884=>array(10,-144,520,485),7885=>array(2,-144,394,353),7886=>array(10,-10,520,675),7887=>array(2,-8,394,539),7888=>array(10,-10,594,706),7889=>array(12,-8,505,586),7890=>array(10,-10,531,706),7891=>array(12,-8,451,586),7892=>array(10,-10,568,763),7893=>array(12,-8,496,633),7894=>array(10,-10,520,748),7895=>array(12,-8,404,600),7896=>array(10,-144,520,636),7897=>array(2,-144,394,500),7898=>array(10,-10,535,636),7899=>array(2,-8,436,500),7900=>array(10,-10,535,636),7901=>array(2,-8,436,500),7902=>array(10,-10,535,679),7903=>array(2,-8,436,543),7904=>array(10,-10,535,592),7905=>array(2,-8,436,456),7906=>array(10,-144,535,527),7907=>array(2,-144,436,379),7908=>array(4,-144,489,476),7909=>array(31,-144,396,350),7910=>array(4,-9,489,675),7911=>array(31,-9,396,539),7912=>array(4,-9,603,636),7913=>array(31,-9,504,500),7914=>array(4,-9,603,636),7915=>array(31,-9,504,500),7916=>array(4,-9,603,675),7917=>array(31,-9,504,539),7918=>array(4,-9,603,592),7919=>array(31,-9,504,456),7920=>array(4,-144,603,527),7921=>array(31,-144,504,371),7922=>array(51,0,459,636),7923=>array(-49,-151,392,500),7924=>array(51,-144,459,476),7925=>array(-49,-151,392,340),7926=>array(51,0,459,675),7927=>array(-49,-151,392,539),7928=>array(51,0,459,592),7929=>array(-49,-151,392,456),8204=>array(-17,-250,18,287),8205=>array(-54,-250,55,287),8206=>array(-17,-250,166,286),8207=>array(-165,-250,18,286),8211=>array(-1,157,326,211),8212=>array(-1,157,684,211),8216=>array(104,328,229,515),8217=>array(111,314,236,501),8218=>array(23,-100,148,87),8220=>array(104,328,360,515),8221=>array(111,314,367,501),8222=>array(23,-100,279,87),8224=>array(54,-33,366,475),8225=>array(18,-33,366,475),8226=>array(29,116,197,284),8230=>array(-1,-9,434,87),8240=>array(35,-9,949,485),8242=>array(52,371,168,476),8243=>array(52,371,257,476),8249=>array(24,47,225,315),8250=>array(-4,47,198,315),8260=>array(-98,0,261,477),8355=>array(-18,0,412,477),8363=>array(-31,-103,491,504),8364=>array(15,-9,537,484),8467=>array(8,-5,264,450),8482=>array(85,180,646,475),8486=>array(-12,0,463,455),8494=>array(-7,-7,322,386),8531=>array(39,-9,503,468),8532=>array(2,-9,503,472),8533=>array(39,-9,528,468),8534=>array(2,-9,528,472),8535=>array(30,-9,528,471),8536=>array(31,-9,528,469),8537=>array(39,-9,500,468),8538=>array(20,-9,500,473),8539=>array(39,-9,510,468),8540=>array(30,-9,510,471),8541=>array(20,-9,510,473),8542=>array(47,-9,510,471),8706=>array(1,-5,367,455),8710=>array(-41,0,433,472),8719=>array(-6,-75,496,476),8721=>array(-34,-75,448,476),8722=>array(52,157,353,211),8730=>array(26,-10,437,472),8734=>array(38,102,597,333),8747=>array(-118,-98,314,484),8776=>array(-3,13,392,279),8800=>array(21,0,385,387),8804=>array(-9,15,398,396),8805=>array(-9,15,371,396),9674=>array(30,0,379,441),9676=>array(50,22,415,387),63232=>array(33,-8,403,432),63233=>array(-407,450,-85,618),63234=>array(-407,450,-52,648),63235=>array(-407,450,-44,658),63236=>array(-407,450,-52,648),63237=>array(-178,464,-86,605),63238=>array(-276,451,8,650),63239=>array(-335,454,8,637),63240=>array(-250,464,-72,603),63241=>array(-231,454,-10,667),63242=>array(-58,464,33,605),63243=>array(-197,451,101,660),63244=>array(-275,454,68,637),63245=>array(-101,464,76,603),63246=>array(-133,454,87,667),63247=>array(14,-8,586,433),63248=>array(-290,477,38,643),63249=>array(-260,465,-76,634),63250=>array(-340,464,-32,697),63251=>array(-106,677,-21,791),63252=>array(-211,667,60,844),63253=>array(-247,668,69,836),63254=>array(-141,677,21,804),63255=>array(-174,639,28,835),63256=>array(-289,-481,-154,-249),63257=>array(-409,-457,-129,-245),63258=>array(-222,-321,-146,-251),63259=>array(-28,-234,399,407),63260=>array(-64,-235,399,407),63261=>array(29,0,525,447),63616=>array(31,-6,399,520),63617=>array(14,-181,577,407),63618=>array(-28,-181,608,407),64257=>array(-139,-150,401,511),64258=>array(-139,-150,417,511));
$cw=array(0=>698,32=>226,33=>168,34=>291,35=>462,36=>367,37=>688,38=>486,39=>169,40=>244,41=>244,42=>343,43=>417,44=>172,45=>226,46=>172,47=>276,48=>378,49=>378,50=>378,51=>378,52=>378,53=>378,54=>378,55=>378,56=>378,57=>378,58=>172,59=>172,60=>417,61=>417,62=>417,63=>289,64=>572,65=>451,66=>396,67=>432,68=>459,69=>358,70=>358,71=>454,72=>472,73=>163,74=>282,75=>411,76=>359,77=>574,78=>472,79=>518,80=>390,81=>518,82=>395,83=>365,84=>421,85=>482,86=>422,87=>612,88=>426,89=>396,90=>451,91=>234,92=>268,93=>234,94=>418,95=>376,96=>210,97=>431,98=>435,99=>347,100=>435,101=>391,102=>254,103=>332,104=>423,105=>163,106=>171,107=>340,108=>213,109=>636,110=>423,111=>412,112=>431,113=>431,114=>246,115=>296,116=>258,117=>418,118=>358,119=>480,120=>349,121=>361,122=>358,123=>255,124=>177,125=>255,126=>422,160=>226,161=>168,162=>480,163=>480,164=>480,165=>480,166=>161,167=>380,168=>341,169=>540,170=>289,171=>356,172=>448,173=>226,174=>540,175=>376,176=>277,177=>440,178=>260,179=>260,180=>259,181=>414,182=>380,183=>172,184=>249,185=>260,186=>294,187=>356,188=>542,189=>542,190=>542,191=>289,192=>451,193=>451,194=>451,195=>451,196=>451,197=>451,198=>583,199=>432,200=>358,201=>358,202=>358,203=>358,204=>163,205=>163,206=>163,207=>163,208=>459,209=>472,210=>518,211=>518,212=>518,213=>518,214=>518,215=>417,216=>518,217=>482,218=>482,219=>482,220=>482,221=>396,222=>394,223=>425,224=>431,225=>431,226=>431,227=>431,228=>431,229=>431,230=>621,231=>347,232=>391,233=>391,234=>391,235=>391,236=>163,237=>163,238=>163,239=>163,240=>378,241=>423,242=>412,243=>412,244=>412,245=>412,246=>412,247=>417,248=>412,249=>418,250=>418,251=>418,252=>418,253=>361,254=>438,255=>361,258=>451,259=>431,262=>432,263=>347,268=>432,269=>347,272=>459,273=>435,286=>454,287=>332,296=>163,297=>163,304=>163,305=>163,321=>359,322=>217,338=>723,339=>689,350=>365,351=>296,352=>365,353=>296,360=>482,361=>418,376=>396,381=>451,382=>358,402=>404,416=>518,417=>412,431=>482,432=>414,710=>218,711=>308,728=>342,729=>193,730=>266,731=>255,732=>259,733=>359,768=>0,769=>0,771=>0,777=>0,803=>0,3584=>0,3585=>391,3586=>415,3587=>431,3588=>403,3589=>403,3590=>453,3591=>312,3592=>390,3593=>405,3594=>415,3595=>431,3596=>565,3597=>565,3598=>425,3599=>425,3600=>372,3601=>492,3602=>551,3603=>576,3604=>405,3605=>405,3606=>391,3607=>438,3608=>358,3609=>433,3610=>445,3611=>445,3612=>414,3613=>414,3614=>482,3615=>482,3616=>425,3617=>417,3618=>386,3619=>329,3620=>391,3621=>399,3622=>425,3623=>379,3624=>403,3625=>461,3626=>399,3627=>438,3628=>468,3629=>394,3630=>385,3631=>380,3632=>340,3633=>0,3634=>343,3635=>343,3636=>0,3637=>0,3638=>0,3639=>0,3640=>0,3641=>0,3642=>0,3647=>389,3648=>208,3649=>395,3650=>252,3651=>269,3652=>252,3653=>209,3654=>437,3655=>0,3656=>0,3657=>0,3658=>0,3659=>0,3660=>0,3661=>0,3662=>0,3663=>450,3664=>456,3665=>456,3666=>456,3667=>456,3668=>456,3669=>456,3670=>456,3671=>456,3672=>456,3673=>456,3674=>525,3675=>697,7840=>451,7841=>431,7842=>451,7843=>431,7844=>451,7845=>431,7846=>451,7847=>431,7848=>451,7849=>431,7850=>451,7851=>431,7852=>451,7853=>431,7854=>451,7855=>431,7856=>451,7857=>431,7858=>451,7859=>431,7860=>451,7861=>431,7862=>451,7863=>431,7864=>358,7865=>391,7866=>358,7867=>391,7868=>358,7869=>391,7870=>358,7871=>391,7872=>358,7873=>391,7874=>358,7875=>391,7876=>358,7877=>391,7878=>358,7879=>391,7880=>163,7881=>163,7882=>163,7883=>163,7884=>518,7885=>412,7886=>518,7887=>412,7888=>518,7889=>432,7890=>518,7891=>432,7892=>518,7893=>432,7894=>518,7895=>432,7896=>518,7897=>412,7898=>518,7899=>432,7900=>518,7901=>432,7902=>518,7903=>432,7904=>518,7905=>432,7906=>518,7907=>432,7908=>482,7909=>418,7910=>482,7911=>418,7912=>482,7913=>414,7914=>482,7915=>414,7916=>482,7917=>414,7918=>482,7919=>414,7920=>482,7921=>414,7922=>396,7923=>361,7924=>396,7925=>361,7926=>396,7927=>361,7928=>396,7929=>361,8204=>0,8205=>0,8206=>0,8207=>0,8211=>335,8212=>693,8216=>253,8217=>253,8218=>253,8220=>384,8221=>384,8222=>384,8224=>380,8225=>380,8226=>226,8230=>504,8240=>972,8242=>131,8243=>220,8249=>234,8250=>234,8260=>147,8355=>393,8363=>435,8364=>518,8467=>254,8482=>608,8486=>476,8494=>349,8531=>542,8532=>542,8533=>542,8534=>542,8535=>542,8536=>542,8537=>542,8538=>542,8539=>542,8540=>542,8541=>542,8542=>542,8706=>349,8710=>480,8719=>493,8721=>417,8722=>417,8730=>387,8734=>632,8747=>248,8776=>417,8800=>417,8804=>417,8805=>417,9674=>405,9676=>406,63232=>372,63233=>0,63234=>0,63235=>0,63236=>0,63237=>0,63238=>0,63239=>0,63240=>0,63241=>0,63242=>0,63243=>0,63244=>0,63245=>0,63246=>0,63247=>565,63248=>0,63249=>0,63250=>0,63251=>0,63252=>0,63253=>0,63254=>0,63255=>0,63256=>0,63257=>0,63258=>0,63259=>425,63260=>425,63261=>475,63616=>449,63617=>598,63618=>629,64257=>405,64258=>455);
// --- EOF ---
