backup-web-drugPurchasing

เวอร์ชั่นก่อนหน้า 1 - 6 เป็นเว็บเบิก อยู่ GithubforDevolopers มีเฉพาะ การเบิกยาไม่มี งานจัดซื้อ


**********************************************************************************************

version 7.5
-ลบ display_report.php ออกเนื่องจากไม่ได้ใช้งาน
แก้ไขชื่อ dashboard.copy.php เป็น withdrawUser.php เนื่องจากเป็นหน้าเบิกยา


**********************************************************************************************

version 7.4

**********************************************************************************************

version 7.3
 - เพิ่มไลบราลี phpword เพื่อให้สามารถสร้างไฟล์ word ได้
 - เพิ่ม ฟ้อน THsarabunnew and thsarabunit
 
 -เพิ่มการจัดการหน้าแรก ขึ้นเรื่องรายงาน (ยังไม่เสร็จ) ไม่แสดงเลขหน้าแรก
 -เพิ่มหน้า รอโหลดข้อมูล 100% ถึงจะดูข้อมูลได้ ให้กับ 
    admin_cancelled_report.php
    admin_po_number_report.php
    all_combined_report.php

Purchasing\indexPurchasing.php
เพิ่มการป้องกัน SQL Injection, CSRF, XSS
ตรวจสอบสิทธิ์การเข้าถึงและบันทึกกิจกรรม
เพิ่มการตรวจสอบการส่งฟอร์มและการทำงานของ JavaScript
ปรับปรุงโค้ดให้มีความปลอดภัยสูงขึ้น

**********************************************************************************************

version 7.2
config.php เพิ่มการเชื่อมต่อแบบ utfmb4 เพื่อรองรับภาษาไทย
generate_pdf.php (ใบเบิก)แก้ไขชื่อและวันที่แสดงผิดพลาดเป็น ดึงจากฐานข้อมูล po โดยตรงมาแสดง

แก้ไข search_item.php ให้รองรับการดึงข้อมูลแบบ utf-8 รองรับภาษาไทยจากฐานข้อมูล

Purchasing\duplicates.php
Purchasing\generate_report.php
Purchasing\indexPurchasing.php
แก้ไขการคำนวน มูลค่ารวมต่อรายการ และมูลค่ารวม/บิล ใหม่ คิดคำนวนจากการคูณกันใหม่แล้วแสดง แทนการ sum จากฐานข้อมูลที่มีข้อมูลมากและทำให้ผิดพลาด

**********************************************************************************************

version 7.1 แก้ไขprocess.php เนื่องจากคำนวณ ราคารวมแต่ละรายการไม่ถูกต้อง

**********************************************************************************************

Version.7.0
เพิ่มรายการหน้าจัดซื้อ หลังเบิกยาเสสร็จแล้วทุกหน่วยเบิก จะประมวลผลผ่าน

1.โฟล์เดอร์ Purchasing>process.php แล้วบันทึกลงฐานข้อมูลตาราง processed
2.เพิ่มการออกใบกำหนดคุณลักษณะ โดยแยก การจัดซื้อ GPO  และ บริษัททั่วไปได้
