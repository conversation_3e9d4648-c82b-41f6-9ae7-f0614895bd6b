<?php
// TCPDF FONT FILE DESCRIPTION
$type='TrueTypeUnicode';
$name='THSarabunNew-Italic';
$up=-35;
$ut=30;
$dw=692;
$diff='';
$originalsize=116800;
$enc='';
$file='thsarabunnewi.z';
$ctg='thsarabunnewi.ctg.z';
$desc=array('Flags'=>96,'FontBBox'=>'[-378 -435 947 836]','ItalicAngle'=>-12,'Ascent'=>844,'Descent'=>-457,'Leading'=>30,'CapHeight'=>476,'XHeight'=>340,'StemV'=>70,'StemH'=>30,'AvgWidth'=>377,'MaxWidth'=>954,'MissingWidth'=>692);
$cbbox=array(0=>array(50,0,642,809),33=>array(-2,-6,153,475),34=>array(75,338,234,499),35=>array(15,0,402,475),36=>array(19,-58,343,536),37=>array(31,-9,566,476),38=>array(-9,-9,424,493),39=>array(75,338,146,499),40=>array(-9,-58,240,532),41=>array(-35,-58,212,532),42=>array(50,258,307,494),43=>array(30,0,372,357),44=>array(-32,-109,89,68),45=>array(5,165,193,198),46=>array(5,-6,80,63),47=>array(-67,-29,339,493),48=>array(18,-11,356,479),49=>array(54,0,270,478),50=>array(-17,0,366,478),51=>array(-3,-13,336,478),52=>array(12,0,334,488),53=>array(-12,-8,360,475),54=>array(14,-8,349,484),55=>array(82,0,386,475),56=>array(3,-8,357,479),57=>array(24,-12,359,480),58=>array(5,-6,135,318),59=>array(-32,-109,135,318),60=>array(37,-2,403,377),61=>array(33,102,369,265),62=>array(0,-2,365,377),63=>array(44,-6,293,483),64=>array(-13,-97,524,394),65=>array(-43,0,357,483),66=>array(11,-6,386,482),67=>array(8,-7,433,483),68=>array(12,-4,434,481),69=>array(11,0,371,476),70=>array(11,0,381,476),71=>array(7,-5,423,481),72=>array(11,0,445,476),73=>array(11,0,151,476),74=>array(-42,-9,268,476),75=>array(11,-5,426,482),76=>array(11,0,291,476),77=>array(-3,0,533,476),78=>array(11,0,445,476),79=>array(6,-10,494,484),80=>array(11,0,389,481),81=>array(6,-67,494,484),82=>array(11,0,390,481),83=>array(-10,-9,354,485),84=>array(46,0,442,476),85=>array(9,-9,470,476),86=>array(58,0,448,476),87=>array(58,0,646,476),88=>array(-26,0,452,476),89=>array(58,0,424,476),90=>array(-21,0,453,476),91=>array(-7,-78,249,506),92=>array(50,-28,230,483),93=>array(-48,-78,209,506),94=>array(33,159,357,480),95=>array(-81,-183,276,-152),96=>array(116,390,187,481),97=>array(16,-10,370,347),98=>array(34,-9,382,505),99=>array(2,-9,337,347),100=>array(4,-9,413,505),101=>array(13,-9,344,347),102=>array(-150,-156,303,513),103=>array(-16,-157,312,341),104=>array(9,-7,368,505),105=>array(9,0,156,475),106=>array(-112,-148,168,475),107=>array(9,-10,328,505),108=>array(8,-7,155,505),109=>array(9,-7,561,347),110=>array(9,-7,368,347),111=>array(4,-10,378,347),112=>array(-26,-167,382,347),113=>array(16,-168,373,347),114=>array(9,0,238,347),115=>array(-10,-9,263,347),116=>array(28,-9,251,459),117=>array(7,-9,367,346),118=>array(32,0,367,340),119=>array(32,0,536,340),120=>array(-43,0,340,340),121=>array(-51,-156,363,340),122=>array(-28,0,333,340),123=>array(1,-77,239,483),124=>array(-19,-174,162,528),125=>array(-31,-77,206,483),126=>array(15,151,398,249),161=>array(-25,-140,136,341),162=>array(18,-59,424,542),163=>array(14,0,387,461),164=>array(-5,43,426,410),165=>array(44,19,431,459),166=>array(-9,-123,150,475),167=>array(27,-39,347,479),168=>array(86,392,277,453),169=>array(40,-3,526,483),170=>array(32,241,274,478),171=>array(5,48,296,309),172=>array(43,91,407,272),173=>array(5,165,193,198),174=>array(40,-3,526,483),175=>array(69,528,427,559),176=>array(53,312,239,483),177=>array(-5,0,379,357),178=>array(23,196,256,481),179=>array(37,195,253,482),180=>array(123,528,242,619),181=>array(-15,-140,380,340),182=>array(56,-63,377,475),183=>array(38,147,113,216),184=>array(-34,-139,100,4),185=>array(71,201,210,484),186=>array(42,238,308,483),187=>array(-13,48,277,309),188=>array(39,-9,486,467),189=>array(39,-9,516,467),190=>array(35,-9,486,470),191=>array(-28,-153,222,341),192=>array(-43,0,357,617),193=>array(-43,0,357,617),194=>array(-43,0,357,606),195=>array(-43,0,366,590),196=>array(-43,0,370,589),197=>array(-43,0,359,651),198=>array(-50,0,590,476),199=>array(8,-139,433,483),200=>array(11,0,371,617),201=>array(11,0,371,617),202=>array(11,0,371,606),203=>array(11,0,371,589),204=>array(11,0,186,617),205=>array(11,0,204,617),206=>array(11,0,231,606),207=>array(11,0,249,589),208=>array(7,-4,434,481),209=>array(11,0,445,590),210=>array(6,-10,494,617),211=>array(6,-10,494,617),212=>array(6,-10,494,606),213=>array(6,-10,494,590),214=>array(6,-10,494,589),215=>array(11,11,386,337),216=>array(6,-29,494,493),217=>array(9,-9,470,617),218=>array(9,-9,470,617),219=>array(9,-9,470,606),220=>array(9,-9,470,589),221=>array(58,0,424,617),222=>array(17,0,376,476),223=>array(-31,-148,370,487),224=>array(16,-10,370,481),225=>array(16,-10,370,481),226=>array(16,-10,370,470),227=>array(16,-10,370,454),228=>array(16,-10,371,453),229=>array(16,-10,370,515),230=>array(-8,-9,596,347),231=>array(2,-126,337,347),232=>array(13,-9,344,481),233=>array(13,-9,344,481),234=>array(13,-9,344,470),235=>array(13,-9,344,453),236=>array(9,0,155,481),237=>array(9,0,173,481),238=>array(9,0,200,470),239=>array(9,0,218,453),240=>array(11,-12,339,480),241=>array(9,-7,368,454),242=>array(4,-10,378,481),243=>array(4,-10,378,481),244=>array(4,-10,378,470),245=>array(4,-10,378,454),246=>array(4,-10,378,453),247=>array(30,25,372,336),248=>array(1,-29,384,383),249=>array(7,-9,367,481),250=>array(7,-9,367,481),251=>array(7,-9,367,470),252=>array(7,-9,367,453),253=>array(-51,-156,363,481),254=>array(-11,-140,410,470),255=>array(-51,-156,363,453),258=>array(-43,0,371,589),259=>array(16,-10,370,453),262=>array(8,-7,433,617),263=>array(2,-9,337,481),268=>array(8,-7,433,598),269=>array(2,-9,337,462),272=>array(10,-4,435,481),273=>array(4,-9,451,505),286=>array(7,-5,423,589),287=>array(-16,-157,326,453),296=>array(11,0,240,590),297=>array(9,0,209,454),304=>array(11,0,186,575),305=>array(9,0,119,340),321=>array(17,0,338,476),322=>array(16,-7,194,505),338=>array(6,-10,731,484),339=>array(4,-10,675,347),350=>array(-8,-139,356,485),351=>array(-7,-126,266,347),352=>array(-10,-9,354,598),353=>array(-10,-9,279,462),360=>array(14,-9,475,590),361=>array(7,-9,367,454),376=>array(65,0,431,586),381=>array(-21,0,453,598),382=>array(-28,0,333,462),402=>array(-71,-72,460,462),416=>array(6,-10,531,521),417=>array(4,-10,427,398),431=>array(9,-9,559,521),432=>array(7,-9,455,398),710=>array(92,392,258,470),711=>array(78,384,244,462),728=>array(142,385,321,453),729=>array(185,507,259,575),730=>array(155,368,314,515),731=>array(12,-158,151,4),732=>array(63,391,247,457),733=>array(69,330,296,491),768=>array(-59,390,12,481),769=>array(-87,390,20,481),771=>array(-122,388,62,454),777=>array(-114,384,18,527),803=>array(-168,-106,-106,-49),3585=>array(17,-37,356,400),3586=>array(30,-8,378,427),3587=>array(22,-8,382,427),3588=>array(22,-37,365,400),3589=>array(24,-37,373,402),3590=>array(22,-8,408,427),3591=>array(23,0,271,400),3592=>array(43,-9,353,400),3593=>array(19,-7,351,400),3594=>array(30,-8,419,427),3595=>array(22,-8,423,427),3596=>array(13,-9,519,427),3597=>array(19,-171,519,427),3598=>array(-12,-219,395,400),3599=>array(-34,-220,395,400),3600=>array(-103,-220,373,427),3601=>array(22,-37,447,401),3602=>array(31,-8,532,427),3603=>array(20,-10,543,427),3604=>array(22,-37,361,400),3605=>array(23,-37,358,400),3606=>array(16,-37,348,400),3607=>array(39,-37,416,400),3608=>array(12,-7,352,427),3609=>array(39,-8,420,427),3610=>array(39,-8,428,427),3611=>array(39,-8,459,577),3612=>array(-3,0,381,427),3613=>array(-3,0,412,577),3614=>array(39,0,447,427),3615=>array(39,0,478,577),3616=>array(-12,-38,395,400),3617=>array(-13,-8,400,427),3618=>array(3,-8,375,427),3619=>array(44,-8,341,427),3620=>array(16,-233,348,400),3621=>array(17,-18,346,400),3622=>array(-12,-233,395,400),3623=>array(29,-8,308,400),3624=>array(22,-37,419,427),3625=>array(39,-8,450,427),3626=>array(17,-18,419,427),3627=>array(39,-37,421,400),3628=>array(39,0,502,427),3629=>array(20,-8,360,400),3630=>array(3,-8,404,427),3631=>array(75,-37,373,400),3632=>array(0,15,341,382),3633=>array(-169,464,137,619),3634=>array(43,-37,290,400),3635=>array(-97,-37,290,615),3636=>array(-283,455,9,603),3637=>array(-283,455,38,647),3638=>array(-283,455,64,642),3639=>array(-283,455,38,647),3640=>array(-233,-260,-117,-58),3641=>array(-338,-255,-102,-54),3642=>array(-182,-123,-124,-70),3647=>array(38,-59,367,523),3648=>array(17,-6,164,427),3649=>array(17,-6,337,427),3650=>array(36,-8,297,651),3651=>array(27,-8,245,657),3652=>array(53,-8,220,665),3653=>array(-67,-233,179,400),3654=>array(72,-208,371,413),3655=>array(-231,460,48,688),3656=>array(12,680,69,791),3657=>array(-82,677,174,836),3658=>array(-192,675,99,833),3659=>array(-31,680,111,790),3660=>array(-80,630,117,807),3661=>array(-97,462,69,615),3662=>array(-136,463,67,671),3663=>array(32,-6,400,332),3664=>array(12,-7,412,295),3665=>array(9,-19,411,294),3666=>array(-3,-12,418,460),3667=>array(6,-2,422,294),3668=>array(0,-11,502,414),3669=>array(0,-11,502,420),3670=>array(37,-8,424,414),3671=>array(-4,-3,480,414),3672=>array(-5,-9,456,414),3673=>array(0,-11,458,414),3674=>array(75,-37,506,400),3675=>array(57,24,664,340),7840=>array(-43,-106,357,483),7841=>array(16,-106,370,347),7842=>array(-43,0,360,663),7843=>array(16,-10,370,512),7844=>array(-43,0,457,661),7845=>array(16,-10,429,527),7846=>array(-43,0,409,661),7847=>array(16,-10,387,527),7848=>array(-43,0,462,706),7849=>array(16,-10,446,584),7850=>array(-43,0,390,696),7851=>array(16,-10,376,559),7852=>array(-43,-106,357,606),7853=>array(16,-106,370,470),7854=>array(-43,0,371,679),7855=>array(16,-10,370,546),7856=>array(-43,0,371,681),7857=>array(16,-10,375,545),7858=>array(-43,0,371,729),7859=>array(16,-10,372,596),7860=>array(-43,0,386,686),7861=>array(16,-10,384,548),7862=>array(-43,-106,371,589),7863=>array(16,-106,370,453),7864=>array(11,-106,371,476),7865=>array(13,-106,344,347),7866=>array(11,0,371,663),7867=>array(13,-9,344,512),7868=>array(11,0,371,590),7869=>array(13,-9,344,454),7870=>array(11,0,422,661),7871=>array(13,-9,415,527),7872=>array(11,0,376,661),7873=>array(13,-9,369,528),7874=>array(11,0,443,706),7875=>array(13,-9,420,584),7876=>array(11,0,371,696),7877=>array(13,-9,347,559),7878=>array(11,-106,371,606),7879=>array(13,-106,344,470),7880=>array(11,0,237,663),7881=>array(9,0,206,527),7882=>array(-17,-106,151,476),7883=>array(-18,-106,156,475),7884=>array(6,-106,494,484),7885=>array(4,-106,378,347),7886=>array(6,-10,494,663),7887=>array(4,-10,378,527),7888=>array(6,-10,511,661),7889=>array(4,-10,430,527),7890=>array(6,-10,494,661),7891=>array(4,-10,385,527),7892=>array(6,-10,514,706),7893=>array(4,-10,433,584),7894=>array(6,-10,494,696),7895=>array(4,-10,378,559),7896=>array(6,-106,494,606),7897=>array(4,-106,378,470),7898=>array(6,-10,531,617),7899=>array(4,-10,427,481),7900=>array(6,-10,531,617),7901=>array(4,-10,427,481),7902=>array(6,-10,531,663),7903=>array(4,-10,427,512),7904=>array(6,-10,531,590),7905=>array(4,-10,427,454),7906=>array(6,-106,531,521),7907=>array(4,-106,427,398),7908=>array(9,-106,470,476),7909=>array(7,-106,367,346),7910=>array(9,-9,470,663),7911=>array(7,-9,367,512),7912=>array(9,-9,559,617),7913=>array(7,-9,455,481),7914=>array(9,-9,559,617),7915=>array(7,-9,455,481),7916=>array(9,-9,559,663),7917=>array(7,-9,455,512),7918=>array(9,-9,559,590),7919=>array(7,-9,455,454),7920=>array(9,-106,559,521),7921=>array(7,-106,455,398),7922=>array(58,0,424,617),7923=>array(-51,-156,363,481),7924=>array(58,-106,424,476),7925=>array(-51,-156,363,340),7926=>array(58,0,424,663),7927=>array(-51,-156,363,512),7928=>array(58,0,424,590),7929=>array(-51,-156,363,454),8204=>array(-14,-154,15,383),8205=>array(-51,-154,52,383),8206=>array(-14,-154,163,382),8207=>array(-162,-154,15,382),8211=>array(13,165,337,198),8212=>array(14,165,694,198),8216=>array(105,334,230,515),8217=>array(102,322,227,503),8218=>array(10,-111,135,70),8220=>array(110,334,351,515),8221=>array(107,322,348,503),8222=>array(15,-111,256,70),8224=>array(57,-34,360,474),8225=>array(19,-34,360,474),8226=>array(24,116,192,284),8230=>array(5,-6,399,63),8240=>array(31,-9,810,476),8242=>array(50,371,166,476),8243=>array(50,371,255,476),8249=>array(5,48,185,309),8250=>array(-13,48,166,309),8260=>array(-79,0,256,476),8355=>array(-36,0,387,476),8363=>array(-39,-108,451,505),8364=>array(-11,-7,433,483),8467=>array(22,-3,223,448),8482=>array(72,242,546,492),8486=>array(-8,0,436,447),8494=>array(8,-7,297,384),8531=>array(40,-9,510,467),8532=>array(13,-9,510,471),8533=>array(40,-9,522,467),8534=>array(13,-9,522,471),8535=>array(36,-9,522,470),8536=>array(44,-9,522,482),8537=>array(40,-9,503,467),8538=>array(29,-9,503,467),8539=>array(39,-9,507,467),8540=>array(35,-9,507,470),8541=>array(28,-9,507,467),8542=>array(57,-9,507,467),8706=>array(-4,-5,341,453),8710=>array(-43,0,431,472),8719=>array(0,-55,450,476),8721=>array(-29,-55,440,476),8722=>array(47,165,355,198),8730=>array(28,-10,407,472),8734=>array(45,107,577,325),8747=>array(-102,-103,303,482),8776=>array(17,75,388,306),8800=>array(33,-9,369,354),8804=>array(17,16,358,341),8805=>array(17,16,333,341),9674=>array(32,0,352,398),9676=>array(-26,22,339,387),63232=>array(30,-9,357,400),63233=>array(-365,455,-72,603),63234=>array(-365,455,-43,647),63235=>array(-365,455,-17,642),63236=>array(-365,455,-44,647),63237=>array(-142,470,-74,614),63238=>array(-238,465,47,640),63239=>array(-270,475,33,640),63240=>array(-214,470,-60,606),63241=>array(-192,471,15,658),63242=>array(-33,480,33,624),63243=>array(-172,477,112,652),63244=>array(-222,466,81,631),63245=>array(-78,476,77,615),63246=>array(-123,471,84,658),63247=>array(19,-9,519,427),63248=>array(-276,463,30,618),63249=>array(-238,462,-71,615),63250=>array(-310,460,-30,688),63251=>array(-69,680,-12,791),63252=>array(-187,677,69,836),63253=>array(-242,675,49,833),63254=>array(-113,680,29,790),63255=>array(-134,634,73,821),63256=>array(-273,-435,-157,-245),63257=>array(-378,-435,-142,-242),63258=>array(-204,-299,-146,-246),63259=>array(-12,-219,395,400),63260=>array(-34,-220,395,400),63261=>array(39,0,502,427),63616=>array(32,-6,400,546),63617=>array(16,-233,549,400),63618=>array(-12,-233,595,400),64257=>array(-152,-156,344,513),64258=>array(-152,-156,356,513));
$cw=array(0=>692,32=>216,33=>147,34=>208,35=>403,36=>361,37=>585,38=>423,39=>120,40=>190,41=>190,42=>285,43=>411,44=>162,45=>216,46=>162,47=>270,48=>362,49=>362,50=>362,51=>362,52=>362,53=>362,54=>362,55=>362,56=>362,57=>362,58=>162,59=>162,60=>411,61=>411,62=>411,63=>283,64=>536,65=>400,66=>378,67=>406,68=>431,69=>351,70=>351,71=>425,72=>441,73=>147,74=>264,75=>376,76=>353,77=>548,78=>441,79=>486,80=>378,81=>487,82=>379,83=>352,84=>379,85=>466,86=>390,87=>588,88=>418,89=>366,90=>424,91=>196,92=>262,93=>196,94=>412,95=>352,96=>204,97=>398,98=>401,99=>331,100=>401,101=>374,102=>206,103=>311,104=>390,105=>143,106=>155,107=>316,108=>200,109=>601,110=>390,111=>398,112=>401,113=>401,114=>217,115=>282,116=>238,117=>390,118=>341,119=>507,120=>318,121=>337,122=>321,123=>208,124=>153,125=>208,126=>416,160=>216,161=>151,162=>411,163=>411,164=>411,165=>411,166=>153,167=>374,168=>212,169=>536,170=>272,171=>298,172=>420,173=>216,174=>536,175=>352,176=>212,177=>411,178=>254,179=>254,180=>212,181=>406,182=>374,183=>162,184=>212,185=>254,186=>285,187=>294,188=>536,189=>536,190=>536,191=>289,192=>400,193=>400,194=>400,195=>400,196=>400,197=>400,198=>582,199=>406,200=>351,201=>351,202=>351,203=>351,204=>147,205=>147,206=>147,207=>147,208=>431,209=>441,210=>486,211=>486,212=>486,213=>486,214=>486,215=>411,216=>486,217=>466,218=>466,219=>466,220=>466,221=>366,222=>380,223=>392,224=>398,225=>398,226=>398,227=>398,228=>398,229=>398,230=>608,231=>331,232=>374,233=>374,234=>374,235=>374,236=>143,237=>143,238=>143,239=>143,240=>362,241=>390,242=>398,243=>398,244=>398,245=>398,246=>398,247=>411,248=>398,249=>390,250=>390,251=>390,252=>390,253=>337,254=>432,255=>337,258=>400,259=>401,262=>406,263=>331,268=>406,269=>331,272=>431,273=>401,286=>425,287=>311,296=>147,297=>143,304=>147,305=>143,321=>361,322=>209,338=>711,339=>679,350=>357,351=>288,352=>352,353=>282,360=>476,361=>405,376=>382,381=>424,382=>321,402=>398,416=>486,417=>398,431=>466,432=>373,710=>212,711=>212,728=>302,729=>302,730=>302,731=>302,732=>212,733=>279,768=>0,769=>0,771=>0,777=>0,803=>0,3584=>0,3585=>386,3586=>378,3587=>382,3588=>393,3589=>393,3590=>408,3591=>294,3592=>367,3593=>377,3594=>380,3595=>384,3596=>519,3597=>519,3598=>425,3599=>425,3600=>343,3601=>461,3602=>532,3603=>543,3604=>391,3605=>391,3606=>378,3607=>430,3608=>335,3609=>420,3610=>428,3611=>428,3612=>381,3613=>381,3614=>447,3615=>447,3616=>425,3617=>400,3618=>375,3619=>322,3620=>378,3621=>381,3622=>425,3623=>335,3624=>393,3625=>438,3626=>381,3627=>427,3628=>454,3629=>387,3630=>372,3631=>391,3632=>357,3633=>0,3634=>316,3635=>316,3636=>0,3637=>0,3638=>0,3639=>0,3640=>0,3641=>0,3642=>0,3647=>411,3648=>203,3649=>377,3650=>237,3651=>242,3652=>244,3653=>205,3654=>399,3655=>0,3656=>0,3657=>0,3658=>0,3659=>0,3660=>0,3661=>0,3662=>0,3663=>450,3664=>449,3665=>449,3666=>449,3667=>449,3668=>449,3669=>449,3670=>449,3671=>449,3672=>449,3673=>449,3674=>522,3675=>697,7840=>400,7841=>398,7842=>400,7843=>398,7844=>400,7845=>398,7846=>400,7847=>398,7848=>400,7849=>398,7850=>400,7851=>398,7852=>400,7853=>398,7854=>400,7855=>398,7856=>400,7857=>398,7858=>400,7859=>398,7860=>400,7861=>398,7862=>400,7863=>398,7864=>351,7865=>374,7866=>351,7867=>374,7868=>351,7869=>374,7870=>351,7871=>374,7872=>351,7873=>374,7874=>351,7875=>374,7876=>351,7877=>374,7878=>351,7879=>374,7880=>147,7881=>143,7882=>147,7883=>143,7884=>486,7885=>398,7886=>486,7887=>398,7888=>486,7889=>398,7890=>486,7891=>398,7892=>486,7893=>398,7894=>486,7895=>398,7896=>486,7897=>398,7898=>486,7899=>398,7900=>486,7901=>398,7902=>486,7903=>398,7904=>486,7905=>398,7906=>486,7907=>398,7908=>466,7909=>390,7910=>466,7911=>390,7912=>466,7913=>373,7914=>466,7915=>373,7916=>466,7917=>373,7918=>466,7919=>373,7920=>466,7921=>373,7922=>366,7923=>337,7924=>366,7925=>337,7926=>366,7927=>337,7928=>366,7929=>337,8204=>0,8205=>0,8206=>0,8207=>0,8211=>360,8212=>720,8216=>247,8217=>247,8218=>247,8220=>370,8221=>370,8222=>370,8224=>374,8225=>374,8226=>216,8230=>479,8240=>829,8242=>125,8243=>214,8249=>183,8250=>183,8260=>162,8355=>360,8363=>401,8364=>406,8467=>209,8482=>563,8486=>453,8494=>313,8531=>536,8532=>536,8533=>536,8534=>536,8535=>536,8536=>536,8537=>536,8538=>536,8539=>536,8540=>536,8541=>536,8542=>536,8706=>317,8710=>474,8719=>446,8721=>411,8722=>411,8730=>350,8734=>618,8747=>242,8776=>411,8800=>411,8804=>411,8805=>411,9674=>386,9676=>400,63232=>336,63233=>0,63234=>0,63235=>0,63236=>0,63237=>0,63238=>0,63239=>0,63240=>0,63241=>0,63242=>0,63243=>0,63244=>0,63245=>0,63246=>0,63247=>512,63248=>0,63249=>0,63250=>0,63251=>0,63252=>0,63253=>0,63254=>0,63255=>0,63256=>0,63257=>0,63258=>0,63259=>425,63260=>425,63261=>454,63616=>443,63617=>575,63618=>621,64257=>347,64258=>404);
// --- EOF ---
